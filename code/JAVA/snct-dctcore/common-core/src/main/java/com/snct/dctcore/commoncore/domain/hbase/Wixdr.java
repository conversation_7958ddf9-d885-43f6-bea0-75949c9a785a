package com.snct.dctcore.commoncore.domain.hbase;

import com.snct.dctcore.commoncore.domain.Instrument;

/**
 * 传感器数据解析类
 * 
 * 用于解析NMEA $WIXDR格式数据，包含温度、湿度、气压等多种传感器值
 * 示例: $WIXDR,C,17.0,C,0,H,74.0,P,0,P,1014.3,H,0*7C
 * 
 * 字段含义:
 * 1. C - 传感器类型（温度）
 * 2. 17.0 - 温度值
 * 3. C - 温度单位 (摄氏度)
 * 4. 0 - 温度传感器ID
 * 5. H - 传感器类型（湿度）
 * 6. 74.0 - 湿度值
 * 7. P - 湿度单位 (百分比)
 * 8. 0 - 湿度传感器ID
 * 9. P - 传感器类型（气压）
 * 10. 1014.3 - 气压值
 * 11. H - 气压单位
 * 12. 0 - 气压传感器ID
 * 
 * <AUTHOR>
 * @date 2025-06-04
 */
public class Wixdr extends Instrument {
    private String airTemType;     // 温度传感器类型
    private String airTemperature; // 温度值
    private String airUnit;        // 温度单位
    private String airSensor;      // 温度传感器ID
    
    private String humidityType;   // 湿度传感器类型
    private String humidity;       // 湿度值
    private String humidityUnit;   // 湿度单位
    private String humiditySensor; // 湿度传感器ID
    
    private String pointTemType;   // 露点温度传感器类型
    private String pointTem;       // 露点温度值
    private String pointTemSensor; // 露点温度传感器ID
    
    private String pressureType;   // 气压传感器类型
    private String pressure;       // 气压值
    private String pressureUnit;   // 气压单位
    private String pressureSensor; // 气压传感器ID
    
    private String qfeType;        // QFE传感器类型
    private String qfe;            // QFE值
    private String qfeUnit;        // QFE单位
    private String qfeId;          // QFE传感器ID
    
    private String qnhType;        // QNH传感器类型
    private String qnh;            // QNH值
    private String qnhUnit;        // QNH单位
    private String qnhId;          // QNH传感器ID
    
    private String dpType;         // 露点温度传感器类型
    private String dp;             // 露点温度值
    private String dpUnit;         // 露点温度单位
    private String dpId;           // 露点温度传感器ID

    @Override
    public void dataAnalysis(String dataStr) {
        String[] values = super.valuesTrim(dataStr.split(",", -1));
        this.airTemType = values[1];
        this.airTemperature = values[2];
        this.airUnit = values[3];
        this.airSensor = values[4];
        this.humidityType = values[5];
        this.humidity = values[6];
        this.humidityUnit = values[7];
        this.humiditySensor = values[8];
        this.pressureType = values[9];
        this.pressure = values[10];
        this.qfe = values[10];
        this.qnh = values[10];
        this.pressureSensor = values[11];
    }

    public void dataAnalysis1(String dataStr) {
        String[] values = super.valuesTrim(dataStr.split(",", -1));
        if (values[1].equalsIgnoreCase("H")) {
            if (values[4].equalsIgnoreCase("RH")) {
                this.humidityType = values[1];
                this.humidity = values[2];
                this.humidityUnit = values[3];
                this.humiditySensor = values[4];
            }
            if (values[8].substring(0, 2).equalsIgnoreCase("AT")) {
                this.airTemType = values[5];
                this.airTemperature = values[6];
                this.airUnit = values[7];
                this.airSensor = values[8];
                return;
            }
            return;
        }
        if (values[1].equalsIgnoreCase("G")) {
            if (values[4].substring(0, 2).equalsIgnoreCase("SR")) {
                this.pointTemType = values[1];
                this.pointTem = values[2];
                this.pointTemSensor = values[4];
                return;
            }
            return;
        }
        if (values[1].equalsIgnoreCase("P")) {
            if (values[4].substring(0, 2).equalsIgnoreCase("S1")) {
                this.pressureType = values[1];
                this.pressure = values[2];
                this.pressureUnit = values[3];
                this.pressureSensor = values[4];
            }
            if (values[4].equalsIgnoreCase("QFE")) {
                this.qfeType = values[1];
                this.qfe = values[2];
                this.qfeUnit = values[3];
                this.qfeId = values[4];
            }
            if (values.length > 8 && values[8].equalsIgnoreCase("QNH")) {
                this.qnhType = values[5];
                this.qnh = values[6];
                this.qnhUnit = values[7];
                this.qnhId = values[8];
            }
            if (values.length >= 20 && values[20].substring(0, 2).equalsIgnoreCase("DP")) {
                this.dpType = values[17];
                this.dp = values[18];
                this.dpUnit = values[19];
                this.dpId = values[20];
                return;
            }
            return;
        }
        if (values[1].equalsIgnoreCase("C") && values[4].substring(0, 2).equalsIgnoreCase("AT")) {
            this.airTemType = values[1];
            this.airTemperature = values[2];
            this.airUnit = values[3];
            this.airSensor = values[4];
        }
    }

    public String getPressureUnit() {
        return this.pressureUnit;
    }

    public void setPressureUnit(String pressureUnit) {
        this.pressureUnit = pressureUnit;
    }

    public String getAirTemType() {
        return this.airTemType;
    }

    public void setAirTemType(String airTemType) {
        this.airTemType = airTemType;
    }

    public String getAirTemperature() {
        return this.airTemperature;
    }

    public void setAirTemperature(String airTemperature) {
        this.airTemperature = airTemperature;
    }

    public String getAirUnit() {
        return this.airUnit;
    }

    public void setAirUnit(String airUnit) {
        this.airUnit = airUnit;
    }

    public String getAirSensor() {
        return this.airSensor;
    }

    public void setAirSensor(String airSensor) {
        this.airSensor = airSensor;
    }

    public String getHumidityType() {
        return this.humidityType;
    }

    public void setHumidityType(String humidityType) {
        this.humidityType = humidityType;
    }

    public String getHumidity() {
        return this.humidity;
    }

    public void setHumidity(String humidity) {
        this.humidity = humidity;
    }

    public String getHumidityUnit() {
        return this.humidityUnit;
    }

    public void setHumidityUnit(String humidityUnit) {
        this.humidityUnit = humidityUnit;
    }

    public String getHumiditySensor() {
        return this.humiditySensor;
    }

    public void setHumiditySensor(String humiditySensor) {
        this.humiditySensor = humiditySensor;
    }

    public String getPointTemType() {
        return this.pointTemType;
    }

    public void setPointTemType(String pointTemType) {
        this.pointTemType = pointTemType;
    }

    public String getPointTem() {
        return this.pointTem;
    }

    public void setPointTem(String pointTem) {
        this.pointTem = pointTem;
    }

    public String getPointTemSensor() {
        return this.pointTemSensor;
    }

    public void setPointTemSensor(String pointTemSensor) {
        this.pointTemSensor = pointTemSensor;
    }

    public String getPressureType() {
        return this.pressureType;
    }

    public void setPressureType(String pressureType) {
        this.pressureType = pressureType;
    }

    public String getPressure() {
        return this.pressure;
    }

    public void setPressure(String pressure) {
        this.pressure = pressure;
    }

    public String getPressureSensor() {
        return this.pressureSensor;
    }

    public void setPressureSensor(String pressureSensor) {
        this.pressureSensor = pressureSensor;
    }

    public String getQfeType() {
        return this.qfeType;
    }

    public void setQfeType(String qfeType) {
        this.qfeType = qfeType;
    }

    public String getQfe() {
        return this.qfe;
    }

    public void setQfe(String qfe) {
        this.qfe = qfe;
    }

    public String getQfeUnit() {
        return this.qfeUnit;
    }

    public void setQfeUnit(String qfeUnit) {
        this.qfeUnit = qfeUnit;
    }

    public String getQfeId() {
        return this.qfeId;
    }

    public void setQfeId(String qfeId) {
        this.qfeId = qfeId;
    }

    public String getQnhType() {
        return this.qnhType;
    }

    public void setQnhType(String qnhType) {
        this.qnhType = qnhType;
    }

    public String getQnh() {
        return this.qnh;
    }

    public void setQnh(String qnh) {
        this.qnh = qnh;
    }

    public String getQnhUnit() {
        return this.qnhUnit;
    }

    public void setQnhUnit(String qnhUnit) {
        this.qnhUnit = qnhUnit;
    }

    public String getQnhId() {
        return this.qnhId;
    }

    public void setQnhId(String qnhId) {
        this.qnhId = qnhId;
    }

    public String getDpType() {
        return this.dpType;
    }

    public void setDpType(String dpType) {
        this.dpType = dpType;
    }

    public String getDp() {
        return this.dp;
    }

    public void setDp(String dp) {
        this.dp = dp;
    }

    public String getDpUnit() {
        return this.dpUnit;
    }

    public void setDpUnit(String dpUnit) {
        this.dpUnit = dpUnit;
    }

    public String getDpId() {
        return this.dpId;
    }

    public void setDpId(String dpId) {
        this.dpId = dpId;
    }
}