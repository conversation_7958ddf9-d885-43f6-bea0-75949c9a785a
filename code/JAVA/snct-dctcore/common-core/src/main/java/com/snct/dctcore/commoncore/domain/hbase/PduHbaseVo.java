package com.snct.dctcore.commoncore.domain.hbase;


import com.snct.dctcore.commoncore.annotation.Excel;
import com.snct.dctcore.commoncore.annotation.HBaseColumn;
import com.snct.dctcore.commoncore.annotation.HBaseTable;

/**
 * @ClassName: PduHbaseVo
 * @Description: PDU数据HBase存储实体类
 * @author: wzewei
 * @date: 2025-08-12 14:44
 */
@HBaseTable
public class PduHbaseVo {

    @HBaseColumn(family = "rowkey", qualifier = "rowkey")
    private String id;

    /**
     * 录入时间
     */
    @HBaseColumn(family = "i", qualifier = "time")
    private String initialTime;

    /**
     * 录入时间(北京时间yyyy-MM-dd HH:mm:ss)
     */
    @Excel(name="录入时间")
    @HBaseColumn(family = "i", qualifier = "bj_time")
    private String initialBjTime;

    /** 总电能 */
    @Excel(name = "总电能")
    @HBaseColumn(family = "i", qualifier = "manage")
    private String manage;

    /** 电流 */
    @Excel(name = "电流")
    @HBaseColumn(family = "i", qualifier = "e_t")
    private String electric;

    /** 电压 */
    @Excel(name = "电压")
    @HBaseColumn(family = "i", qualifier = "v_t")
    private String voltage;

    /** 有功功率 */
    @Excel(name = "有功功率")
    @HBaseColumn(family = "i", qualifier = "y_p")
    private String yesPower;

    /** 无功功率 */
    @Excel(name = "无功功率")
    @HBaseColumn(family = "i", qualifier = "n_p")
    private String noPower;

    /** 视在功率 */
    @Excel(name = "视在功率")
    @HBaseColumn(family = "i", qualifier = "s_p")
    private String seePower;

    /** 功率因数 */
    @Excel(name = "功率因数")
    @HBaseColumn(family = "i", qualifier = "p_p")
    private String powerParam;

    // region ==================== 通道字段 ====================

    /** 通道1电流 */
    @Excel(name = "通道1电流")
    @HBaseColumn(family = "o", qualifier = "o1_e")
    private String out1Electric;

    /** 通道1功率 */
    @Excel(name = "通道1功率")
    @HBaseColumn(family = "o", qualifier = "o1_p")
    private String out1Power;

    /** 通道1状态 */
    @Excel(name = "通道1状态")
    @HBaseColumn(family = "o", qualifier = "o1_s")
    private String out1Status;

    /** 通道2电流 */
    @Excel(name = "通道2电流")
    @HBaseColumn(family = "o", qualifier = "o2_et")
    private String out2Electric;

    /** 通道2功率 */
    @Excel(name = "通道2功率")
    @HBaseColumn(family = "o", qualifier = "o2_p")
    private String out2Power;

    /** 通道2状态 */
    @Excel(name = "通道2状态")
    @HBaseColumn(family = "o", qualifier = "o2_s")
    private String out2Status;

    /** 通道3电流 */
    @Excel(name = "通道3电流")
    @HBaseColumn(family = "o", qualifier = "o3_e")
    private String out3Electric;

    /** 通道3功率 */
    @Excel(name = "通道3功率")
    @HBaseColumn(family = "o", qualifier = "o3_p")
    private String out3Power;

    /** 通道3状态 */
    @Excel(name = "通道3状态")
    @HBaseColumn(family = "o", qualifier = "o3_s")
    private String out3Status;

    /** 通道4电流 */
    @Excel(name = "通道4电流")
    @HBaseColumn(family = "o", qualifier = "o4_e")
    private String out4Electric;

    /** 通道4功率 */
    @Excel(name = "通道4功率")
    @HBaseColumn(family = "o", qualifier = "o4_p")
    private String out4Power;

    /** 通道4状态 */
    @Excel(name = "通道4状态")
    @HBaseColumn(family = "o", qualifier = "o4_s")
    private String out4Status;

    /** 通道5电流 */
    @Excel(name = "通道5电流")
    @HBaseColumn(family = "o", qualifier = "o5_e")
    private String out5Electric;

    /** 通道5功率 */
    @Excel(name = "通道5功率")
    @HBaseColumn(family = "o", qualifier = "o5_p")
    private String out5Power;

    /** 通道5状态 */
    @Excel(name = "通道5状态")
    @HBaseColumn(family = "o", qualifier = "o5_s")
    private String out5Status;

    /** 通道6电流 */
    @Excel(name = "通道6电流")
    @HBaseColumn(family = "o", qualifier = "o6_e")
    private String out6Electric;

    /** 通道6功率 */
    @Excel(name = "通道6功率")
    @HBaseColumn(family = "o", qualifier = "o6_p")
    private String out6Power;

    /** 通道6状态 */
    @Excel(name = "通道6状态")
    @HBaseColumn(family = "o", qualifier = "o6_s")
    private String out6Status;

    /** 通道7电流 */
    @Excel(name = "通道7电流")
    @HBaseColumn(family = "o", qualifier = "o7_e")
    private String out7Electric;

    /** 通道7功率 */
    @Excel(name = "通道7功率")
    @HBaseColumn(family = "o", qualifier = "o7_p")
    private String out7Power;

    /** 通道7状态 */
    @Excel(name = "通道7状态")
    @HBaseColumn(family = "o", qualifier = "o7_s")
    private String out7Status;

    /** 通道8电流 */
    @Excel(name = "通道8电流")
    @HBaseColumn(family = "o", qualifier = "o8_e")
    private String out8Electric;

    /** 通道8功率 */
    @Excel(name = "通道8功率")
    @HBaseColumn(family = "o", qualifier = "o8_p")
    private String out8Power;

    /** 通道8状态 */
    @Excel(name = "通道8状态")
    @HBaseColumn(family = "o", qualifier = "o8_s")
    private String out8Status;
    
    // endregion =================== 通道字段 ====================
    
    /**
     * 设置指定通道的数据
     *
     * @param channelIndex 通道索引（1-8）
     * @param electric 电流
     * @param power 功率
     * @param status 插座状态
     */
    public void setChannelData(int channelIndex, String electric, String power, String status) {
        switch(channelIndex) {
            case 1:
                this.out1Electric = electric;
                this.out1Power = power;
                this.out1Status = status;
                break;
            case 2:
                this.out2Electric = electric;
                this.out2Power = power;
                this.out2Status = status;
                break;
            case 3:
                this.out3Electric = electric;
                this.out3Power = power;
                this.out3Status = status;
                break;
            case 4:
                this.out4Electric = electric;
                this.out4Power = power;
                this.out4Status = status;
                break;
            case 5:
                this.out5Electric = electric;
                this.out5Power = power;
                this.out5Status = status;
                break;
            case 6:
                this.out6Electric = electric;
                this.out6Power = power;
                this.out6Status = status;
                break;
            case 7:
                this.out7Electric = electric;
                this.out7Power = power;
                this.out7Status = status;
                break;
            case 8:
                this.out8Electric = electric;
                this.out8Power = power;
                this.out8Status = status;
                break;
            default:
                throw new IllegalArgumentException("通道索引必须在1-8之间，当前值: " + channelIndex);
        }
    }

    /**
     * 获取指定通道的电流
     */
    public String getChannelElectric(int channelIndex) {
        switch(channelIndex) {
            case 1: return out1Electric;
            case 2: return out2Electric;
            case 3: return out3Electric;
            case 4: return out4Electric;
            case 5: return out5Electric;
            case 6: return out6Electric;
            case 7: return out7Electric;
            case 8: return out8Electric;
            default: throw new IllegalArgumentException("通道索引必须在1-8之间，当前值: " + channelIndex);
        }
    }

    /**
     * 获取指定通道的功率
     */
    public String getChannelPower(int channelIndex) {
        switch(channelIndex) {
            case 1: return out1Power;
            case 2: return out2Power;
            case 3: return out3Power;
            case 4: return out4Power;
            case 5: return out5Power;
            case 6: return out6Power;
            case 7: return out7Power;
            case 8: return out8Power;
            default: throw new IllegalArgumentException("通道索引必须在1-8之间，当前值: " + channelIndex);
        }
    }

    /**
     * 获取指定通道的状态
     */
    public String getChannelStatus(int channelIndex) {
        switch(channelIndex) {
            case 1: return out1Status;
            case 2: return out2Status;
            case 3: return out3Status;
            case 4: return out4Status;
            case 5: return out5Status;
            case 6: return out6Status;
            case 7: return out7Status;
            case 8: return out8Status;
            default: throw new IllegalArgumentException("通道索引必须在1-8之间，当前值: " + channelIndex);
        }
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getInitialTime() {
        return initialTime;
    }

    public void setInitialTime(String initialTime) {
        this.initialTime = initialTime;
    }

    public String getInitialBjTime() {
        return initialBjTime;
    }

    public void setInitialBjTime(String initialBjTime) {
        this.initialBjTime = initialBjTime;
    }

    public String getManage() {
        return manage;
    }

    public void setManage(String manage) {
        this.manage = manage;
    }

    public String getElectric() {
        return electric;
    }

    public void setElectric(String electric) {
        this.electric = electric;
    }

    public String getVoltage() {
        return voltage;
    }

    public void setVoltage(String voltage) {
        this.voltage = voltage;
    }

    public String getYesPower() {
        return yesPower;
    }

    public void setYesPower(String yesPower) {
        this.yesPower = yesPower;
    }

    public String getNoPower() {
        return noPower;
    }

    public void setNoPower(String noPower) {
        this.noPower = noPower;
    }

    public String getSeePower() {
        return seePower;
    }

    public void setSeePower(String seePower) {
        this.seePower = seePower;
    }

    public String getPowerParam() {
        return powerParam;
    }

    public void setPowerParam(String powerParam) {
        this.powerParam = powerParam;
    }

    // ==================== 通道字段的Getter/Setter方法 ====================

    public String getOut1Electric() { return out1Electric; }
    public void setOut1Electric(String out1Electric) { this.out1Electric = out1Electric; }

    public String getOut1Power() { return out1Power; }
    public void setOut1Power(String out1Power) { this.out1Power = out1Power; }

    public String getOut1Status() { return out1Status; }
    public void setOut1Status(String out1Status) { this.out1Status = out1Status; }

    public String getOut2Electric() { return out2Electric; }
    public void setOut2Electric(String out2Electric) { this.out2Electric = out2Electric; }

    public String getOut2Power() { return out2Power; }
    public void setOut2Power(String out2Power) { this.out2Power = out2Power; }

    public String getOut2Status() { return out2Status; }
    public void setOut2Status(String out2Status) { this.out2Status = out2Status; }

    public String getOut3Electric() { return out3Electric; }
    public void setOut3Electric(String out3Electric) { this.out3Electric = out3Electric; }

    public String getOut3Power() { return out3Power; }
    public void setOut3Power(String out3Power) { this.out3Power = out3Power; }

    public String getOut3Status() { return out3Status; }
    public void setOut3Status(String out3Status) { this.out3Status = out3Status; }

    public String getOut4Electric() { return out4Electric; }
    public void setOut4Electric(String out4Electric) { this.out4Electric = out4Electric; }

    public String getOut4Power() { return out4Power; }
    public void setOut4Power(String out4Power) { this.out4Power = out4Power; }

    public String getOut4Status() { return out4Status; }
    public void setOut4Status(String out4Status) { this.out4Status = out4Status; }

    public String getOut5Electric() { return out5Electric; }
    public void setOut5Electric(String out5Electric) { this.out5Electric = out5Electric; }

    public String getOut5Power() { return out5Power; }
    public void setOut5Power(String out5Power) { this.out5Power = out5Power; }

    public String getOut5Status() { return out5Status; }
    public void setOut5Status(String out5Status) { this.out5Status = out5Status; }

    public String getOut6Electric() { return out6Electric; }
    public void setOut6Electric(String out6Electric) { this.out6Electric = out6Electric; }

    public String getOut6Power() { return out6Power; }
    public void setOut6Power(String out6Power) { this.out6Power = out6Power; }

    public String getOut6Status() { return out6Status; }
    public void setOut6Status(String out6Status) { this.out6Status = out6Status; }

    public String getOut7Electric() { return out7Electric; }
    public void setOut7Electric(String out7Electric) { this.out7Electric = out7Electric; }

    public String getOut7Power() { return out7Power; }
    public void setOut7Power(String out7Power) { this.out7Power = out7Power; }

    public String getOut7Status() { return out7Status; }
    public void setOut7Status(String out7Status) { this.out7Status = out7Status; }

    public String getOut8Electric() { return out8Electric; }
    public void setOut8Electric(String out8Electric) { this.out8Electric = out8Electric; }

    public String getOut8Power() { return out8Power; }
    public void setOut8Power(String out8Power) { this.out8Power = out8Power; }

    public String getOut8Status() { return out8Status; }
    public void setOut8Status(String out8Status) { this.out8Status = out8Status; }

    /**
     * 重置对象状态，用于对象复用
     */
    public void reset() {
        this.id = null;
        this.initialTime = null;
        this.initialBjTime = null;
        this.manage = null;
        this.electric = null;
        this.voltage = null;
        this.yesPower = null;
        this.noPower = null;
        this.seePower = null;
        this.powerParam = null;

        // 重置所有通道数据
        this.out1Electric = null;
        this.out1Power = null;
        this.out1Status = null;
        this.out2Electric = null;
        this.out2Power = null;
        this.out2Status = null;
        this.out3Electric = null;
        this.out3Power = null;
        this.out3Status = null;
        this.out4Electric = null;
        this.out4Power = null;
        this.out4Status = null;
        this.out5Electric = null;
        this.out5Power = null;
        this.out5Status = null;
        this.out6Electric = null;
        this.out6Power = null;
        this.out6Status = null;
        this.out7Electric = null;
        this.out7Power = null;
        this.out7Status = null;
        this.out8Electric = null;
        this.out8Power = null;
        this.out8Status = null;
    }
}
