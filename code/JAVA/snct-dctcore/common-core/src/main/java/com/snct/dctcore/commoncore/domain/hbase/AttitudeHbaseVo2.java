package com.snct.dctcore.commoncore.domain.hbase;


import com.snct.dctcore.commoncore.annotation.Excel;
import com.snct.dctcore.commoncore.annotation.HBaseColumn;
import com.snct.dctcore.commoncore.annotation.HBaseTable;

/**
 * @ClassName: AttitudeHbaseVo2
 * @Description: 姿态仪数据HBase存储实体类(JMS)
 * @author: wzewei
 * @date: 2025-08-12 13:55
 */
@HBaseTable
public class AttitudeHbaseVo2 {

    /**
     * HBase行键
     */
    @HBaseColumn(family = "rowkey", qualifier = "rowkey")
    private String id;

    /**
     * 录入时间
     */
    @HBaseColumn(family = "i", qualifier = "time")
    private String initialTime;

    /**
     * 录入时间(北京时间yyyy-MM-dd HH:mm:ss)
     */
    @Excel(name = "录入时间")
    @HBaseColumn(family = "i", qualifier = "bj_time")
    private String initialBjTime;

    /**
     * UTC时间(时/分/秒/小数秒)
     */
    @Excel(name = "UTC时间")
    @HBaseColumn(family = "i", qualifier = "utc")
    private String utc;

    /**
     * 设备序列号
     */
    @Excel(name = "设备序列号")
    @HBaseColumn(family = "i", qualifier = "s_n_o")
    private String serialNo;

    /**
     * 纬度
     */
    @Excel(name = "纬度")
    @HBaseColumn(family = "i", qualifier = "lat")
    private String lat;

    /**
     * 经度
     */
    @Excel(name = "经度")
    @HBaseColumn(family = "i", qualifier = "lon")
    private String lon;

    /**
     * 椭球高
     */
    @Excel(name = "椭球高")
    @HBaseColumn(family = "i", qualifier = "e_h")
    private String elpHeight;

    /**
     * 航向角
     */
    @Excel(name = "航向角")
    @HBaseColumn(family = "i", qualifier = "heading")
    private String heading;

    /**
     * 俯仰角
     */
    @Excel(name = "俯仰角")
    @HBaseColumn(family = "i", qualifier = "pitch")
    private String pitch;

    /**
     * 北方向速度
     */
    @Excel(name = "北方向速度")
    @HBaseColumn(family = "i", qualifier = "vel_n")
    private String velN;

    /**
     * 东方向速度
     */
    @Excel(name = "东方向速度")
    @HBaseColumn(family = "i", qualifier = "vel_e")
    private String velE;

    /**
     * 地向速度
     */
    @Excel(name = "地向速度")
    @HBaseColumn(family = "i", qualifier = "vel_d")
    private String velD;

    /**
     * 地面速度
     */
    @Excel(name = "地面速度")
    @HBaseColumn(family = "i", qualifier = "vel_g")
    private String velG;

    /**
     * 高精度坐标北向X轴参考PTNL和PK
     */
    @Excel(name = "坐标北向")
    @HBaseColumn(family = "i", qualifier = "c_n")
    private String coordinateNorthing;

    /**
     * 高精度坐标东向Y轴参考PTNL和PK
     */
    @Excel(name = "坐标东向")
    @HBaseColumn(family = "i", qualifier = "c_e")
    private String coordinateEasting;

    /**
     * 基站坐标下的移动站X坐标(基站坐标为原点)
     */
    @Excel(name = "北距离")
    @HBaseColumn(family = "i", qualifier = "n_d")
    private String northDistance;

    /**
     * 基站坐标下的移动站Y坐标(基站坐标为原点)
     */
    @Excel(name = "东距离")
    @HBaseColumn(family = "i", qualifier = "e_d")
    private String eastDistance;

    /**
     * 定位指示状态
     */
    @Excel(name = "定位指示")
    @HBaseColumn(family = "i", qualifier = "p_i")
    private String positionIndicator;

    /**
     * 定向指示状态
     */
    @Excel(name = "定向指示")
    @HBaseColumn(family = "i", qualifier = "h_i")
    private String headingIndicator;

    /**
     * 主导天线收星数
     */
    @Excel(name = "收星数")
    @HBaseColumn(family = "i", qualifier = "svn")
    private String svn;

    /**
     * 差分延迟
     */
    @Excel(name = "差分延迟")
    @HBaseColumn(family = "i", qualifier = "d_a")
    private String diffAge;

    /**
     * 基准站ID
     */
    @Excel(name = "基准站ID")
    @HBaseColumn(family = "i", qualifier = "s_id")
    private String stationId;

    /**
     * 主导和从站内之间的距离(双天线基线长)
     */
    @Excel(name = "基线长度")
    @HBaseColumn(family = "i", qualifier = "b_l")
    private String baselineLength;

    /**
     * 从站参与解算的卫星数
     */
    @Excel(name = "解算卫星数")
    @HBaseColumn(family = "i", qualifier = "s_sv")
    private String solutionSv;

    /**
     * 横滚角
     */
    @Excel(name = "横滚角")
    @HBaseColumn(family = "i", qualifier = "rolling")
    private String rolling;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getInitialTime() {
        return initialTime;
    }

    public void setInitialTime(String initialTime) {
        this.initialTime = initialTime;
    }

    public String getInitialBjTime() {
        return initialBjTime;
    }

    public void setInitialBjTime(String initialBjTime) {
        this.initialBjTime = initialBjTime;
    }

    public String getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(String serialNo) {
        this.serialNo = serialNo;
    }

    public String getUtc() {
        return utc;
    }

    public void setUtc(String utc) {
        this.utc = utc;
    }

    public String getLat() {
        return lat;
    }

    public void setLat(String lat) {
        this.lat = lat;
    }

    public String getLon() {
        return lon;
    }

    public void setLon(String lon) {
        this.lon = lon;
    }

    public String getElpHeight() {
        return elpHeight;
    }

    public void setElpHeight(String elpHeight) {
        this.elpHeight = elpHeight;
    }

    public String getHeading() {
        return heading;
    }

    public void setHeading(String heading) {
        this.heading = heading;
    }

    public String getPitch() {
        return pitch;
    }

    public void setPitch(String pitch) {
        this.pitch = pitch;
    }

    public String getVelN() {
        return velN;
    }

    public void setVelN(String velN) {
        this.velN = velN;
    }

    public String getVelE() {
        return velE;
    }

    public void setVelE(String velE) {
        this.velE = velE;
    }

    public String getVelD() {
        return velD;
    }

    public void setVelD(String velD) {
        this.velD = velD;
    }

    public String getVelG() {
        return velG;
    }

    public void setVelG(String velG) {
        this.velG = velG;
    }

    public String getCoordinateNorthing() {
        return coordinateNorthing;
    }

    public void setCoordinateNorthing(String coordinateNorthing) {
        this.coordinateNorthing = coordinateNorthing;
    }

    public String getCoordinateEasting() {
        return coordinateEasting;
    }

    public void setCoordinateEasting(String coordinateEasting) {
        this.coordinateEasting = coordinateEasting;
    }

    public String getNorthDistance() {
        return northDistance;
    }

    public void setNorthDistance(String northDistance) {
        this.northDistance = northDistance;
    }

    public String getEastDistance() {
        return eastDistance;
    }

    public void setEastDistance(String eastDistance) {
        this.eastDistance = eastDistance;
    }

    public String getPositionIndicator() {
        return positionIndicator;
    }

    public void setPositionIndicator(String positionIndicator) {
        this.positionIndicator = positionIndicator;
    }

    public String getHeadingIndicator() {
        return headingIndicator;
    }

    public void setHeadingIndicator(String headingIndicator) {
        this.headingIndicator = headingIndicator;
    }

    public String getSvn() {
        return svn;
    }

    public void setSvn(String svn) {
        this.svn = svn;
    }

    public String getDiffAge() {
        return diffAge;
    }

    public void setDiffAge(String diffAge) {
        this.diffAge = diffAge;
    }

    public String getStationId() {
        return stationId;
    }

    public void setStationId(String stationId) {
        this.stationId = stationId;
    }

    public String getBaselineLength() {
        return baselineLength;
    }

    public void setBaselineLength(String baselineLength) {
        this.baselineLength = baselineLength;
    }

    public String getSolutionSv() {
        return solutionSv;
    }

    public void setSolutionSv(String solutionSv) {
        this.solutionSv = solutionSv;
    }

    public String getRolling() {
        return rolling;
    }

    public void setRolling(String rolling) {
        this.rolling = rolling;
    }

    /**
     * 重置对象状态，用于对象复用
     */
    public void reset() {
        this.id = null;
        this.initialTime = null;
        this.initialBjTime = null;
        this.utc = null;
        this.serialNo = null;
        this.lat = null;
        this.lon = null;
        this.elpHeight = null;
        this.heading = null;
        this.pitch = null;
        this.velN = null;
        this.velE = null;
        this.velU = null;
        this.coordinateNorthing = null;
        this.coordinateEasting = null;
        this.northDistance = null;
        this.eastDistance = null;
        this.positionIndicator = null;
        this.headingIndicator = null;
        this.svn = null;
        this.diffAge = null;
        this.stationId = null;
        this.baselineLength = null;
        this.solutionSv = null;
        this.rolling = null;
    }
}
