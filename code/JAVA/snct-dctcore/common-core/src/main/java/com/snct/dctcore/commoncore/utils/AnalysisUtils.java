package com.snct.dctcore.commoncore.utils;

import com.snct.dctcore.commoncore.domain.engineroom.EngineroomData;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * 解析操作工具类
 */
public class AnalysisUtils {

    /**
     * ThreadLocal缓存临时List，避免valuesTrim方法重复创建数组
     */
    private static final ThreadLocal<List<String>> TEMP_LIST =
        ThreadLocal.withInitial(() -> new ArrayList<>(50));

    public static String analysis(EngineroomData engineroomData) {
        return engineroomData.getStatus() + "|" + engineroomData.getSymbol() + "|" + engineroomData.getValue();
    }

    public static String analysisValue(EngineroomData engineroomData) {
        return engineroomData.getValue();
    }

    /**
     * 处理字符串数组，去除每个元素的前后空白
     * 使用ThreadLocal缓存临时List，避免重复创建数组
     *
     * @param values 原始字符串数组
     * @return 处理后的字符串数组
     */
    public static String[] valuesTrim(String[] values) {
        List<String> tempList = TEMP_LIST.get();
        tempList.clear();

        for (String value : values) {
            tempList.add(value.trim());
        }
        return tempList.toArray(new String[0]);
    }
}
