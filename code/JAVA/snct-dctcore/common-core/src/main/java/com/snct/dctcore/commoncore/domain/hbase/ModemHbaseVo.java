package com.snct.dctcore.commoncore.domain.hbase;


import com.snct.dctcore.commoncore.annotation.Excel;
import com.snct.dctcore.commoncore.annotation.HBaseColumn;

/**
 * @ClassName: ModemHbaseVo
 * @Description: 卫星猫数据HBase存储实体类
 * @author: wzewei
 * @date: 2025-08-12 14:54
 */
public class ModemHbaseVo {
    /**
     * ID
     */
    @HBaseColumn(family = "rowkey", qualifier = "rowkey")
    private String id;

    /**
     * 录入时间
     */
    @HBaseColumn(family = "i", qualifier = "time")
    private String initialTime;

    /**
     * 录入时间(北京时间yyyy-MM-dd HH:mm:ss)
     */
    @Excel(name = "录入时间")
    @HBaseColumn(family = "i", qualifier = "bj_time")
    private String initialBjTime;


    /**
     * 信号强度
     */
    @Excel(name = "信号强度")
    @HBaseColumn(family = "i", qualifier = "signal")
    private String signal;

    /**
     * 速率
     */
    @Excel(name = "速率")
    @HBaseColumn(family = "i", qualifier = "speed")
    private String speed;

    /**
     * 发送功率
     */
    @Excel(name = "发送功率")
    @HBaseColumn(family = "i", qualifier = "s_power")
    private String sendPower;

    /**
     * 状态标志
     */
    @Excel(name = "状态标志")
    @HBaseColumn(family = "i", qualifier = "flag")
    private String flag;

    public void setId(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }

    public String getInitialTime() {
        return initialTime;
    }

    public void setInitialTime(String initialTime) {
        this.initialTime = initialTime;
    }

    public String getInitialBjTime() {
        return initialBjTime;
    }

    public void setInitialBjTime(String initialBjTime) {
        this.initialBjTime = initialBjTime;
    }

    public void setSignal(String signal) {
        this.signal = signal;
    }

    public String getSignal() {
        return signal;
    }

    public void setSpeed(String speed) {
        this.speed = speed;
    }

    public String getSpeed() {
        return speed;
    }

    public void setSendPower(String sendPower) {
        this.sendPower = sendPower;
    }

    public String getSendPower() {
        return sendPower;
    }

    public void setFlag(String flag) {
        this.flag = flag;
    }

    public String getFlag() {
        return flag;
    }

    /**
     * 重置对象状态，用于对象复用
     */
    public void reset() {
        this.id = null;
        this.initialTime = null;
        this.initialBjTime = null;
        this.signal = null;
        this.speed = null;
        this.sendPower = null;
        this.flag = null;
    }
}
