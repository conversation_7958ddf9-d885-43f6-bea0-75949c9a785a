package com.snct.dctcore.commoncore.analysis;

import com.snct.dctcore.commoncore.domain.KafkaMessage;
import com.snct.dctcore.commoncore.domain.hbase.GpsHbaseVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import static com.snct.dctcore.commoncore.utils.AnalysisUtils.valuesTrim;

/**
 * @ClassName: GpsAnalysis
 * @Description: GPS数据解析
 * @author: wzewei
 * @date: 2025-09-05 09:28:24
 */
public class GpsAnalysis {

    protected static Logger logger = LoggerFactory.getLogger(GpsAnalysis.class);

    /**
     * 解析单条Kafka消息中的GPS数据
     * 主要处理$GNRMC或$BDRMC开头的NMEA格式数据
     *
     * @param kafkaMessage Kafka消息对象
     * @return 解析后的GPS数据对象
     */
    public static GpsHbaseVo getGpsData(KafkaMessage kafkaMessage) {
        GpsHbaseVo gpsHbaseVo;
        if (StringUtils.isEmpty(kafkaMessage.getMsg())) {
            return null;
        }
        try {
            if (kafkaMessage.getMsg().startsWith("$GNRMC") || kafkaMessage.getMsg().startsWith("$BDRMC")) {
                gpsHbaseVo = new GpsHbaseVo();
                String[] values = valuesTrim(kafkaMessage.getMsg().split(",", -1));
                // 设置UTC时间
                gpsHbaseVo.setUtcTime(values[1].substring(0, 6));
                // 处理纬度和纬度半球
                if (values[4].equalsIgnoreCase("N")) {
                    gpsHbaseVo.setLatitude(values[3] + "1");
                } else if (values[4].equalsIgnoreCase("S")) {
                    gpsHbaseVo.setLatitude(values[3] + "3");
                }
                gpsHbaseVo.setLatitudeHemisphere(values[4]);
                // 处理经度和经度半球
                if (values[6].equalsIgnoreCase("E")) {
                    gpsHbaseVo.setLongitude(values[5] + "2");
                } else if (values[6].equalsIgnoreCase("W")) {
                    gpsHbaseVo.setLongitude(values[5] + "4");
                }
                gpsHbaseVo.setLongitudeHemisphere(values[6]);

                return gpsHbaseVo;
            }
        } catch (Exception e) {
            logger.error("GPS数据解析出错, 消息内容: [{}], 异常详情:", kafkaMessage.getMsg(), e);
        }
        return null;
    }
}
