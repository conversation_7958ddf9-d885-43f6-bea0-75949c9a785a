package com.snct.dctcore.commoncore.domain.hbase;


import com.snct.dctcore.commoncore.annotation.Excel;
import com.snct.dctcore.commoncore.annotation.HBaseColumn;
import com.snct.dctcore.commoncore.annotation.HBaseTable;

/**
 * @ClassName: AttitudeHbaseVo
 * @Description: 姿态仪数据HBase存储实体类
 * @author: wzewei
 * @date: 2025-08-12 13:55
 */
@HBaseTable
public class AttitudeHbaseVo {

    @HBaseColumn(family = "rowkey", qualifier = "rowkey")
    private String id;

    /**
     * 录入时间
     */
    @HBaseColumn(family = "i", qualifier = "time")
    private String initialTime;

    /**
     * 录入时间(北京时间yyyy-MM-dd HH:mm:ss)
     */
    @Excel(name="录入时间")
    @HBaseColumn(family = "i", qualifier = "bj_time")
    private String initialBjTime;

    /**
     * UTC时间
     */
    @Excel(name="UTC时间")
    @HBaseColumn(family = "i", qualifier = "utc")
    private String utcTime;

    /**
     * 纬度
     */
    @Excel(name="纬度")
    @HBaseColumn(family = "i", qualifier = "lat")
    private String lat;

    /**
     * 经度
     */
    @Excel(name="经度")
    @HBaseColumn(family = "i", qualifier = "lon")
    private String lon;

    /**
     * 横摇
     */
    @Excel(name="横摇")
    @HBaseColumn(family = "i", qualifier = "roll")
    private String rolling;

    /**
     * 纵摇
     */
    @Excel(name="纵摇")
    @HBaseColumn(family = "i", qualifier = "pitch")
    private String pitch;

    /**
     * 高度
     */
    @Excel(name="高度")
    @HBaseColumn(family = "i", qualifier = "height")
    private String height;

    /**
     * 航向
     */
    @Excel(name="航向")
    @HBaseColumn(family = "i", qualifier = "heading")
    private String heading;

    /**
     * 速度
     */
    @Excel(name="速度")
    @HBaseColumn(family = "i", qualifier = "speed")
    private String speed;

    /**
     * 距离
     */
    @Excel(name = "距离")
    @HBaseColumn(family = "i", qualifier = "distance")
    private String distance;

    /**
     * 站名
     */
    @Excel(name = "站名")
    @HBaseColumn(family = "i", qualifier = "station_name")
    private String stationName;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getInitialTime() {
        return initialTime;
    }

    public void setInitialTime(String initialTime) {
        this.initialTime = initialTime;
    }

    public String getInitialBjTime() {
        return initialBjTime;
    }

    public void setInitialBjTime(String initialBjTime) {
        this.initialBjTime = initialBjTime;
    }

    public String getUtcTime() {
        return utcTime;
    }

    public void setUtcTime(String utcTime) {
        this.utcTime = utcTime;
    }

    public String getLat() {
        return lat;
    }

    public void setLat(String lat) {
        this.lat = lat;
    }

    public String getLon() {
        return lon;
    }

    public void setLon(String lon) {
        this.lon = lon;
    }

    public String getRolling() {
        return rolling;
    }

    public void setRolling(String rolling) {
        this.rolling = rolling;
    }

    public String getPitch() {
        return pitch;
    }

    public void setPitch(String pitch) {
        this.pitch = pitch;
    }

    public String getHeight() {
        return height;
    }

    public void setHeight(String height) {
        this.height = height;
    }

    public String getHeading() {
        return heading;
    }

    public void setHeading(String heading) {
        this.heading = heading;
    }

    public String getSpeed() {
        return speed;
    }

    public void setSpeed(String speed) {
        this.speed = speed;
    }

    public String getDistance() {
        return distance;
    }

    public void setDistance(String distance) {
        this.distance = distance;
    }

    public String getStationName() {
        return stationName;
    }

    public void setStationName(String stationName) {
        this.stationName = stationName;
    }

    /**
     * 重置对象状态，用于对象复用
     */
    public void reset() {
        this.id = null;
        this.initialTime = null;
        this.initialBjTime = null;
        this.utcTime = null;
        this.lat = null;
        this.lon = null;
        this.rolling = null;
        this.pitch = null;
        this.height = null;
        this.heading = null;
        this.speed = null;
        this.distance = null;
        this.stationName = null;
    }
}
