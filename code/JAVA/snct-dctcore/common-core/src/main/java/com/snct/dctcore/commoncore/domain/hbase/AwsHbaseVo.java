package com.snct.dctcore.commoncore.domain.hbase;


import com.snct.dctcore.commoncore.annotation.Excel;
import com.snct.dctcore.commoncore.annotation.HBaseColumn;
import com.snct.dctcore.commoncore.annotation.HBaseTable;

/**
 * @ClassName: AwsHbaseVo
 * @Description: AWS数据HBase存储实体类
 * @author: wzewei
 * @date: 2025-08-12 13:39
 */
@HBaseTable
public class AwsHbaseVo {

    @HBaseColumn(family = "rowkey", qualifier = "rowkey")
    private String id;
    /**
     * 录入时间
     */
    @HBaseColumn(family = "i", qualifier = "time")
    private String initialTime;

    /**
     * 录入时间(北京时间yyyy-MM-dd HH:mm:ss)
     */
    @Excel(name="录入时间")
    @HBaseColumn(family = "i", qualifier = "bj_time")
    private String initialBjTime;

    /**
     * 纬度
     */
    @HBaseColumn(family = "i", qualifier = "lat")
    private String latitude;
    /**
     * 经度
     */
    @HBaseColumn(family = "i", qualifier = "lon")
    private String longitude;


    //  region ------------------ $WIMWV ------------------

    /**
     * 相对风向
     */
    @Excel(name="相对风向")
    @HBaseColumn(family = "i", qualifier = "r_wind")
    private String relativeWind;

    /**
     * 相对风向标识
     */
    @Excel(name="相对风向标识")
    @HBaseColumn(family = "i", qualifier = "r_flag")
    private String windLogoR;

    /**
     * 相对风速
     */
    @Excel(name="相对风速")
    @HBaseColumn(family = "i", qualifier = "rws")
    private String relativeWindSpeed;

    /**
     * 真实风向
     */
    @Excel(name="真实风向")
    @HBaseColumn(family = "i", qualifier = "t_wind")
    private String trueWind;

    /**
     * 真实风速
     */
    @Excel(name="真实风速")
    @HBaseColumn(family = "i", qualifier = "tws")
    private String trueWindSpeed;

    /**
     * 真实风向标识
     */
    @Excel(name="真实风向标识")
    @HBaseColumn(family = "i", qualifier = "t_flag")
    private String windLogoT;

    /**
     * 风速单位 K=千米/小时  M=米/秒，N=海里/小时
     */
    @Excel(name="风速单位")
    @HBaseColumn(family = "i", qualifier = "wsu")
    private String windSpeedUnit;

    // endregion  ------------------ $WIMWV ------------------


    // region  ------------------ $WIXDR ------------------

    /**
     * 传感器类型（气温）
     */
    @Excel(name="传感器类型（气温）")
    @HBaseColumn(family = "i", qualifier = "att")
    private String airTemType;

    /**
     * 气温值（6个字节）
     */
    @Excel(name="气温值")
    @HBaseColumn(family = "i", qualifier = "temp")
    private String airTemperature;

    /**
     * 表示气温值单位（°C）
     */
    @Excel(name="气温值单位（°C）")
    @HBaseColumn(family = "i", qualifier = "tu")
    private String airUnit;
    /**
     * 气温传感器ID
     */
    @Excel(name="气温传感器ID")
    @HBaseColumn(family = "i", qualifier = "as")
    private String airSensor;
    /**
     * 传感器类型（相对湿度）
     */
    @Excel(name="传感器类型（相对湿度）")
    @HBaseColumn(family = "i", qualifier = "ht")
    private String humidityType;

    /**
     * 相对湿度数值（4个字节）
     */
    @Excel(name="相对湿度数值")
    @HBaseColumn(family = "i", qualifier = "hum")
    private String humidity;

    /**
     * 标识相对湿度的单位
     */
    @Excel(name="标识相对湿度的单位")
    @HBaseColumn(family = "i", qualifier = "hu")
    private String humidityUnit;
    /**
     * 部分数据单位和值为一个数据
     * 相对湿度传感器ID
     */
    @Excel(name="相对湿度传感器ID")
    @HBaseColumn(family = "i", qualifier = "hs")
    private String humiditySensor;
    /**
     * 传感器类型（露点温度）
     */
    @Excel(name="传感器类型")
    @HBaseColumn(family = "i", qualifier = "ptt")
    private String pointTemType;
    /**
     * 露点温度数值（6个字节） 数据带单位
     */
    @Excel(name="露点温度数值")
    @HBaseColumn(family = "i", qualifier = "point_tem")
    private String pointTem;
    /**
     * 露点温度传感器ID
     */
    @Excel(name="露点温度传感器ID")
    @HBaseColumn(family = "i", qualifier = "pts")
    private String pointTemSensor;
    /**
     * 传感器类型（气压）
     */
    @Excel(name="传感器类型")
    @HBaseColumn(family = "i", qualifier = "pt")
    private String pressureType;
    /**
     * 气压数值（6个字节），数据带单位
     */
    @Excel(name="气压数值")
    @HBaseColumn(family = "i", qualifier = "press")
    private String pressure;
    /**
     * 气压传感器ID
     */
    @Excel(name="气压传感器ID")
    @HBaseColumn(family = "i", qualifier = "ps")
    private String pressureSensor;

    // endregion  ------------------ $WIXDR ------------------


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getRelativeWind() {
        return relativeWind;
    }

    public void setRelativeWind(String relativeWind) {
        this.relativeWind = relativeWind;
    }

    public String getWindLogoR() {
        return windLogoR;
    }

    public void setWindLogoR(String windLogoR) {
        this.windLogoR = windLogoR;
    }

    public String getRelativeWindSpeed() {
        return relativeWindSpeed;
    }

    public void setRelativeWindSpeed(String relativeWindSpeed) {
        this.relativeWindSpeed = relativeWindSpeed;
    }

    public String getTrueWind() {
        return trueWind;
    }

    public void setTrueWind(String trueWind) {
        this.trueWind = trueWind;
    }

    public String getTrueWindSpeed() {
        return trueWindSpeed;
    }

    public void setTrueWindSpeed(String trueWindSpeed) {
        this.trueWindSpeed = trueWindSpeed;
    }

    public String getWindLogoT() {
        return windLogoT;
    }

    public void setWindLogoT(String windLogoT) {
        this.windLogoT = windLogoT;
    }

    public String getAirTemperature() {
        return airTemperature;
    }

    public void setAirTemperature(String airTemperature) {
        this.airTemperature = airTemperature;
    }

    public String getHumidity() {
        return humidity;
    }

    public void setHumidity(String humidity) {
        this.humidity = humidity;
    }

    public String getInitialTime() {
        return initialTime;
    }

    public void setInitialTime(String initialTime) {
        this.initialTime = initialTime;
    }

    public String getInitialBjTime() {
        return initialBjTime;
    }

    public void setInitialBjTime(String initialBjTime) {
        this.initialBjTime = initialBjTime;
    }

    public String getPointTem() {
        return pointTem;
    }

    public void setPointTem(String pointTem) {
        this.pointTem = pointTem;
    }

    public String getPressure() {
        return pressure;
    }

    public void setPressure(String pressure) {
        this.pressure = pressure;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getWindSpeedUnit() {
        return windSpeedUnit;
    }

    public void setWindSpeedUnit(String windSpeedUnit) {
        this.windSpeedUnit = windSpeedUnit;
    }

    public String getAirTemType() {
        return airTemType;
    }

    public void setAirTemType(String airTemType) {
        this.airTemType = airTemType;
    }

    public String getAirUnit() {
        return airUnit;
    }

    public void setAirUnit(String airUnit) {
        this.airUnit = airUnit;
    }

    public String getAirSensor() {
        return airSensor;
    }

    public void setAirSensor(String airSensor) {
        this.airSensor = airSensor;
    }

    public String getHumidityType() {
        return humidityType;
    }

    public void setHumidityType(String humidityType) {
        this.humidityType = humidityType;
    }

    public String getHumidityUnit() {
        return humidityUnit;
    }

    public void setHumidityUnit(String humidityUnit) {
        this.humidityUnit = humidityUnit;
    }

    public String getHumiditySensor() {
        return humiditySensor;
    }

    public void setHumiditySensor(String humiditySensor) {
        this.humiditySensor = humiditySensor;
    }

    public String getPointTemType() {
        return pointTemType;
    }

    public void setPointTemType(String pointTemType) {
        this.pointTemType = pointTemType;
    }

    public String getPointTemSensor() {
        return pointTemSensor;
    }

    public void setPointTemSensor(String pointTemSensor) {
        this.pointTemSensor = pointTemSensor;
    }

    public String getPressureType() {
        return pressureType;
    }

    public void setPressureType(String pressureType) {
        this.pressureType = pressureType;
    }

    public String getPressureSensor() {
        return pressureSensor;
    }

    public void setPressureSensor(String pressureSensor) {
        this.pressureSensor = pressureSensor;
    }

    /**
     * 重置对象状态，用于对象复用
     */
    public void reset() {
        this.id = null;
        this.initialTime = null;
        this.initialBjTime = null;
        this.latitude = null;
        this.longitude = null;

        // 重置风向风速相关字段
        this.relativeWind = null;
        this.windLogoR = null;
        this.relativeWindSpeed = null;
        this.trueWind = null;
        this.trueWindSpeed = null;
        this.windLogoT = null;
        this.windSpeedUnit = null;

        // 重置传感器相关字段
        this.airTemType = null;
        this.airTemperature = null;
        this.airUnit = null;
        this.airSensor = null;
        this.humidityType = null;
        this.humidity = null;
        this.humidityUnit = null;
        this.humiditySensor = null;
        this.pointTemType = null;
        this.pointTem = null;
        this.pointTemSensor = null;
        this.pressureType = null;
        this.pressure = null;
        this.pressureSensor = null;
    }
}
