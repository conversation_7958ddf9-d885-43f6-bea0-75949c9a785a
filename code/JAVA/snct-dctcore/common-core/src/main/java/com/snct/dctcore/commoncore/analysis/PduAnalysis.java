package com.snct.dctcore.commoncore.analysis;


import cn.hutool.core.util.HexUtil;
import com.snct.dctcore.commoncore.domain.KafkaMessage;
import com.snct.dctcore.commoncore.domain.hbase.PduHbaseVo;

import com.snct.dctcore.commoncore.utils.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;

/**
 * @ClassName: PduAnalysis
 * @Description: PDU数据解析
 * @author: wzewei
 * @date: 2025-09-05 10:48
 */
public class PduAnalysis {
    protected static Logger logger = LoggerFactory.getLogger(PduAnalysis.class);

    /**
     * ThreadLocal缓存PduHbaseVo对象，避免频繁创建新对象
     */
    private static final ThreadLocal<PduHbaseVo> PDU_CACHE =
        ThreadLocal.withInitial(PduHbaseVo::new);

    /**
     * 解析PDU数据
     *
     * @param kafkaMessage
     * @return
     */
    public static PduHbaseVo getPduData(KafkaMessage kafkaMessage) {
        if (kafkaMessage == null || kafkaMessage.getMsg() == null || kafkaMessage.getMsg().isEmpty()) {
            return null;
        }
        PduHbaseVo pduHbaseVo = null;

        try {
            // 当前时间
            long currentTime = kafkaMessage.getInitialTime() == null ? System.currentTimeMillis() :
                    kafkaMessage.getInitialTime();

            String[] rawDataArray = kafkaMessage.getMsg().split(",");
            if (rawDataArray.length == 0) {
                return null;
            }

            // 从ThreadLocal缓存中获取对象并重置状态
            pduHbaseVo = PDU_CACHE.get();
            pduHbaseVo.reset();
            pduHbaseVo.setInitialTime(Objects.requireNonNull(DateUtils.fetchWholeSecond(currentTime)).toString());
            pduHbaseVo.setInitialBjTime(DateUtils.parseTimeToDate(currentTime, "yyyy-MM-dd HH:mm:ss"));

            // 遍历所有原始数据，统一解析
            for (String hexData : rawDataArray) {
                if (hexData == null || hexData.trim().length() < 4) {
                    continue;
                }

                hexData = hexData.trim();
                String command = hexData.substring(0, 4);

                switch (command) {
                    case "AA0C": // 通道数据
                        parseChannelData(hexData, pduHbaseVo);
                        break;
                    case "AA08": // 系统数据（电流/电压）
                        parseSystemData(hexData, pduHbaseVo);
                        break;
                    case "AA0A": // 总电能
                        parseTotalEnergy(hexData, pduHbaseVo);
                        break;
                    case "AA0E": // 总功率
                        parseTotalPower(hexData, pduHbaseVo);
                        break;
                    default:
                        logger.debug("未知PDU命令类型: {}", command);
                }
            }
        } catch (Exception e) {
            logger.error("PDU数据解析出错, 消息内容: [{}], 异常详情:", kafkaMessage.getMsg(), e);
        }
        return pduHbaseVo;
    }

    /**
     * 解析通道数据 (AA0C)
     */
    private static void parseChannelData(String hexData, PduHbaseVo pduHbaseVo) {
        if (hexData.length() < 22) {
            return;
        }

        try {
            // 获取通道号
            String channelStr = hexData.substring(10, 12);
            int channel = HexUtil.hexToInt(channelStr);

            // 获取电流
            String currentStr = hexData.substring(12, 16);
            int currentRaw = HexUtil.hexToInt(currentStr);
            double current = currentRaw / 10.0;

            // 获取功率
            String powerStr = hexData.substring(16, 20);
            int powerRaw = HexUtil.hexToInt(powerStr);
            double power = powerRaw / 10.0;

            // 获取状态
            String statusStr = hexData.substring(20, 22);
            Integer status = HexUtil.hexToInt(statusStr);

            // 直接设置到PDU对象
            pduHbaseVo.setChannelData(channel,
                    String.valueOf(current),
                    String.valueOf(power),
                    String.valueOf(status));

            logger.debug("解析通道数据 - 通道: {}, 电流: {}A, 功率: {}W, 状态: {}",
                    channel, current, power, status);

        } catch (Exception e) {
            logger.error("解析通道数据失败: {}", hexData, e);
        }
    }

    /**
     * 解析系统数据 (AA08)
     */
    private static void parseSystemData(String hexData, PduHbaseVo pduHbaseVo) {
        if (hexData.length() < 14) {
            return;
        }

        try {
            // 获取消息类型
            String typeStr = hexData.substring(6, 8);
            // 获取数值
            String valueStr = hexData.substring(10, 14);
            int valueRaw = HexUtil.hexToInt(valueStr);
            double value = valueRaw / 10.0;

            if ("02".equals(typeStr)) {
                // 输入电流
                pduHbaseVo.setElectric(String.valueOf(value));
                logger.debug("解析系统电流: {}A", value);
            } else if ("03".equals(typeStr)) {
                // 输入电压
                pduHbaseVo.setVoltage(String.valueOf(value));
                logger.debug("解析系统电压: {}V", value);
            }

        } catch (Exception e) {
            logger.error("解析PUU系统数据失败: {}", hexData, e);
        }
    }

    /**
     * 解析总电能数据 (AA0A)
     */
    private static void parseTotalEnergy(String hexData, PduHbaseVo pduHbaseVo) {
        if (hexData.length() < 18) {
            return;
        }

        try {
            // 解析总电能值
            String valueStr = hexData.substring(10, 18);
            int valueRaw = HexUtil.hexToInt(valueStr);

            // 计算总电能实际值：总电能 = valueRaw / (2^16 * 10)
            double totalEnergy = valueRaw / (Math.pow(2, 16) * 10);

            pduHbaseVo.setManage(String.valueOf(totalEnergy));

            logger.debug("解析总电能: {}kWh (原始值: {})", totalEnergy, valueRaw);

        } catch (Exception e) {
            logger.error("解析总电能数据失败: {}", hexData, e);
        }
    }

    /**
     * 解析总功率数据 (AA0E)
     */
    private static void parseTotalPower(String hexData, PduHbaseVo pduHbaseVo) {
        if (hexData.length() < 22) {
            return;
        }

        try {
            // 获取有功功率
            String activePowerStr = hexData.substring(10, 14);
            int activePowerRaw = HexUtil.hexToInt(activePowerStr);
            double activePower = activePowerRaw / 10.0;
            pduHbaseVo.setYesPower(String.valueOf(activePower));

            // 获取无功功率
            String reactivePowerStr = hexData.substring(14, 18);
            int reactivePowerRaw = HexUtil.hexToInt(reactivePowerStr);
            double reactivePower = reactivePowerRaw / 10.0;
            pduHbaseVo.setNoPower(String.valueOf(reactivePower));

            // 获取视在功率
            String apparentPowerStr = hexData.substring(18, 22);
            int apparentPowerRaw = HexUtil.hexToInt(apparentPowerStr);
            double apparentPower = apparentPowerRaw / 10.0;
            pduHbaseVo.setSeePower(String.valueOf(apparentPower));

            // 获取功率因数
            if (hexData.length() >= 24) {
                String powerFactorStr = hexData.substring(22, 24);
                Integer powerFactor = HexUtil.hexToInt(powerFactorStr);
                pduHbaseVo.setPowerParam(String.valueOf(powerFactor));
            }

            logger.debug("解析总功率 - 有功: {}kW, 无功: {}kW, 视在: {}kW",
                    activePower, reactivePower, apparentPower);

        } catch (Exception e) {
            logger.error("解析总功率数据失败: {}", hexData, e);
        }
    }
}
